#!/usr/bin/env node

/**
 * 数据库连接测试脚本
 * 用于验证 MySQL 数据库配置是否正确
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../.env.test') });

// 数据库配置
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'mailadmin',
  password: process.env.DB_PASSWORD || 'HOUsc@0202',
  database: process.env.DB_NAME || 'mailserver',
};

// 颜色输出函数
const colors = {
  green: (text) => `\x1b[32m${text}\x1b[0m`,
  red: (text) => `\x1b[31m${text}\x1b[0m`,
  yellow: (text) => `\x1b[33m${text}\x1b[0m`,
  blue: (text) => `\x1b[34m${text}\x1b[0m`,
  cyan: (text) => `\x1b[36m${text}\x1b[0m`,
};

async function testDatabaseConnection() {
  console.log(colors.cyan('🔍 开始测试数据库连接...\n'));

  // 显示配置信息
  console.log(colors.blue('📋 数据库配置:'));
  console.log(`   主机: ${config.host}`);
  console.log(`   端口: ${config.port}`);
  console.log(`   用户: ${config.user}`);
  console.log(`   数据库: ${config.database}`);
  console.log(`   密码: ${config.password ? '***' : '(未设置)'}\n`);

  let connection;

  try {
    // 测试基本连接
    console.log(colors.yellow('1. 测试基本连接...'));
    connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
    });
    console.log(colors.green('   ✅ 基本连接成功\n'));

    // 测试数据库是否存在
    console.log(colors.yellow('2. 检查数据库是否存在...'));
    try {
      const [databases] = await connection.execute('SHOW DATABASES');
      const dbExists = databases.some(row => Object.values(row)[0] === config.database);

      if (!dbExists) {
        console.log(colors.red(`   ❌ 数据库 '${config.database}' 不存在`));
        console.log(colors.yellow('   💡 请先创建数据库:'));
        console.log(`      CREATE DATABASE ${config.database} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;`);
        return false;
      }
      console.log(colors.green(`   ✅ 数据库 '${config.database}' 存在\n`));

      // 连接到指定数据库
      await connection.changeUser({ database: config.database });
    } catch (error) {
      console.log(colors.red(`   ❌ 检查数据库时出错: ${error.message}`));
      return false;
    }

    // 检查表是否存在
    console.log(colors.yellow('3. 检查数据表...'));
    try {
      const [tables] = await connection.execute('SHOW TABLES');

      const expectedTables = ['users', 'folders', 'emails', 'attachments', 'contacts'];
      const existingTables = tables.map(row => Object.values(row)[0]);

      console.log(`   现有表: ${existingTables.length > 0 ? existingTables.join(', ') : '无'}`);

      const missingTables = expectedTables.filter(table => !existingTables.includes(table));

      if (missingTables.length > 0) {
        console.log(colors.yellow(`   ⚠️  缺少表: ${missingTables.join(', ')}`));
        console.log(colors.yellow('   💡 请运行数据库初始化脚本:'));
        console.log('      npm run db:init');
      } else {
        console.log(colors.green('   ✅ 所有必需的表都存在'));
      }
      console.log();
    } catch (error) {
      console.log(colors.red(`   ❌ 检查表时出错: ${error.message}`));
      return false;
    }

    // 测试数据查询
    console.log(colors.yellow('4. 测试数据查询...'));

    try {
      if (existingTables.includes('users')) {
        const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
        const userCount = users[0].count;
        console.log(`   用户数量: ${userCount}`);

        if (userCount > 0) {
          const [testUser] = await connection.execute('SELECT email FROM users LIMIT 1');
          console.log(`   示例用户: ${testUser[0].email}`);
        }
      }

      if (existingTables.includes('emails')) {
        const [emails] = await connection.execute('SELECT COUNT(*) as count FROM emails');
        console.log(`   邮件数量: ${emails[0].count}`);
      }

      if (existingTables.includes('folders')) {
        const [folders] = await connection.execute('SELECT COUNT(*) as count FROM folders');
        console.log(`   文件夹数量: ${folders[0].count}`);
      }

      console.log(colors.green('\n   ✅ 数据查询成功\n'));
    } catch (error) {
      console.log(colors.red(`   ❌ 数据查询失败: ${error.message}`));
      // 不返回 false，因为这可能只是因为表为空
    }

    // 测试写入权限
    console.log(colors.yellow('5. 测试写入权限...'));
    try {
      await connection.execute('CREATE TEMPORARY TABLE test_write_permission (id INT)');
      await connection.execute('DROP TEMPORARY TABLE test_write_permission');
      console.log(colors.green('   ✅ 写入权限正常\n'));
    } catch (error) {
      console.log(colors.red('   ❌ 写入权限测试失败'));
      console.log(`   错误: ${error.message}\n`);
    }

    console.log(colors.green('🎉 数据库连接测试完成！所有测试通过。'));
    return true;

  } catch (error) {
    console.log(colors.red('\n❌ 数据库连接测试失败:'));
    console.log(`错误类型: ${error.code || 'UNKNOWN'}`);
    console.log(`错误信息: ${error.message}`);
    
    // 提供解决建议
    console.log(colors.yellow('\n💡 可能的解决方案:'));
    
    if (error.code === 'ECONNREFUSED') {
      console.log('   - 检查 MySQL 服务是否运行');
      console.log('   - 验证主机和端口配置');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('   - 检查用户名和密码');
      console.log('   - 验证用户权限');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('   - 检查数据库名称');
      console.log('   - 确认数据库已创建');
    } else {
      console.log('   - 检查网络连接');
      console.log('   - 验证防火墙设置');
      console.log('   - 查看 MySQL 错误日志');
    }
    
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
if (require.main === module) {
  testDatabaseConnection()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error(colors.red('测试脚本执行失败:'), error);
      process.exit(1);
    });
}

module.exports = testDatabaseConnection;
