```mysql
-- 创建数据库
CREATE DATABASE mydatabase;

-- 创建表
CREATE TABLE mytable (
  id INT(11) NOT NULL AUTO_INCREMENT,
  name VARCHAR(50) NOT NULL,
  email VARCHAR(50) NOT NULL,
  PRIMARY KEY (id)
);

-- 插入数据
INSERT INTO mytable (name, email) VALUES ('<PERSON>', 'joh<PERSON><PERSON>@example.com');
INSERT INTO mytable (name, email) VALUES ('<PERSON>', 'jane<PERSON>@example.com');
INSERT INTO mytable (name, email) VALUES ('<PERSON>', 'b<PERSON><PERSON><PERSON><PERSON>@example.com');

-- 查询数据
SELECT * FROM mytable;

-- 更新数据
UPDATE virtual_aliases SET destination='<EMAIL>'  WHERE id=1;
{SHA512-CRYPT}$6$AO5rfhFaJOQZsblb$.k4gnuF9iUI.HssLm1p6ObzXUEL3PaXizPk7hfSR9x.WT4F4q2AuiOiSUyBbSJtQ4V5J/t9BjoZZdzwY5ca36.
-- 删除数据
DELETE FROM mytable WHERE id=3;
UPDATE virtual_aliases SET source='<EMAIL>'  WHERE id=1;
INSERT INTO virtual_users (domain_id, email, password) VALUES (1, '<EMAIL>', '$(doveadm pw -s SHA512-CRYPT -p "123qwe!@#")');
INSERT INTO virtual_users (domain_id, email, password) VALUES (1, '<EMAIL>', $(doveadm pw -s SHA512-CRYPT -p "123qwe!@#"));

密码 加密
doveadm pw -s SHA512-CRYPT -p "user_password"

{SHA512-CRYP}$6$HKUOCw9HCTDkDSpt$XJgcMutfBEyJS0cPPohmupHCNFxSHteutQ3vhTBROez3DRZS8hpvRx4EYPlUlBDA2LuI2ZyhWotyUQNkGHmHs0

{SHA512-CRYPT}$6$GDoXrX4metcGo7K7$dMKQjhcB1RSfW2ClPwR6pE3loU2IrvtGZDWVgnkoCrh7scUAe8tQciRTAw.qkkI85/klExN9rOjaZjGdUjk451

INSERT INTO virtual_users (domain_id, email, password)
VALUES (
  (SELECT id FROM virtual_domains WHERE domain_name='blindedby.love'),
  '<EMAIL>',
  '{SHA512-CRYPT}$6$GDoXrX4metcGo7K7$dMKQjhcB1RSfW2ClPwR6pE3loU2IrvtGZDWVgnkoCrh7scUAe8tQciRTAw.qkkI85/klExN9rOjaZjGdUjk451'
);


先测试SMTP发送：
先实现发送功能，这相对简单
使用测试邮箱验证发送功能
处理附件：
确保前端正确上传附件
后端保存附件并在发送邮件时附加
安全考虑：
不要在代码中硬编码邮件凭证
使用环境变量或安全的凭证管理
考虑使用OAuth2而不是密码认证
错误处理：
添加适当的错误处理和重试机制
记录邮件发送失败的情况



CREATE USER 'mailadmin'@'%' IDENTIFIED BY 'HOUsc@0202';
GRANT ALL PRIVILEGES ON *.* TO 'mailadmin'@'%';
FLUSH PRIVILEGES;
