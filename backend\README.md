# 邮箱系统后端API

基于 Node.js + Express + TypeScript 构建的邮箱系统后端API服务。

## 🚀 技术栈

- **Node.js** - JavaScript运行时
- **Express** - Web框架
- **TypeScript** - 类型安全的JavaScript
- **MySQL** - 关系型数据库
- **JWT** - 身份认证
- **bcryptjs** - 密码加密
- **Winston** - 日志管理
- **Helmet** - 安全中间件
- **CORS** - 跨域资源共享

## 📁 项目结构

```
src/
├── controllers/        # 控制器
│   ├── authController.ts
│   ├── emailController.ts
│   └── folderController.ts
├── middleware/         # 中间件
│   ├── auth.ts
│   └── errorHandler.ts
├── routes/            # 路由
│   ├── auth.ts
│   ├── emails.ts
│   └── folders.ts
├── database/          # 数据存储
│   ├── mysqlDatabase.ts
│   └── seedData.ts
├── config/            # 配置文件
│   └── database.ts
├── types/             # 类型定义
│   └── index.ts
├── utils/             # 工具函数
│   └── logger.ts
├── app.ts             # 应用配置
└── index.ts           # 服务器入口
```

## 🛠️ 开发指南

### 环境要求

- Node.js 18+
- npm 或 yarn
- MySQL 5.7+

### 安装依赖

```bash
npm install
```

### 数据库设置

1. 安装并启动 MySQL 服务器
2. 创建数据库：

```sql
CREATE DATABASE email_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. 初始化数据库表和数据：

```bash
npm run db:init
```

### 环境配置

复制 `.env.example` 文件为 `.env` 并配置相关参数：

```env
# 服务器配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:5173

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your-mysql-password
DB_NAME=email_system

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 启动生产服务器
```bash
npm start
```

## 📚 API文档

### 认证相关

#### 用户注册
```
POST /api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "username",
  "displayName": "Display Name",
  "password": "password123"
}
```

#### 用户登录
```
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### 获取当前用户信息
```
GET /api/auth/me
Authorization: Bearer <token>
```

### 邮件相关

#### 获取邮件列表
```
GET /api/emails?folderId=inbox&page=1&limit=20
Authorization: Bearer <token>
```

#### 获取邮件详情
```
GET /api/emails/:id
Authorization: Bearer <token>
```

#### 发送邮件
```
POST /api/emails
Authorization: Bearer <token>
Content-Type: application/json

{
  "to": ["<EMAIL>"],
  "subject": "邮件主题",
  "content": "邮件内容"
}
```

#### 更新邮件状态
```
PUT /api/emails/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "isRead": true,
  "isStarred": false
}
```

#### 删除邮件
```
DELETE /api/emails/:id
Authorization: Bearer <token>
```

#### 搜索邮件
```
POST /api/emails/search
Authorization: Bearer <token>
Content-Type: application/json

{
  "query": "搜索关键词",
  "from": "<EMAIL>"
}
```

### 文件夹相关

#### 获取文件夹列表
```
GET /api/folders
Authorization: Bearer <token>
```

#### 获取文件夹详情
```
GET /api/folders/:id
Authorization: Bearer <token>
```

## 🔒 安全特性

- JWT Token认证
- 密码bcrypt加密
- 请求频率限制
- CORS跨域保护
- Helmet安全头
- 输入验证和清理

## 📊 日志管理

- 使用Winston进行日志记录
- 分级日志（error, warn, info, debug）
- 日志文件轮转
- 请求日志记录

## 🧪 测试用户

系统预置了测试用户：
- 邮箱：<EMAIL>
- 密码：123456

## 🚀 部署

### Docker部署
```bash
# 构建镜像
docker build -t email-system-backend .

# 运行容器
docker run -p 3001:3001 email-system-backend
```

### 传统部署
```bash
# 构建
npm run build

# 启动
npm start
```

## 📝 开发说明

当前版本已升级为 MySQL 数据库，支持数据持久化存储。

### 数据库特性

- 使用 MySQL 作为主数据库
- 支持完整的 CRUD 操作
- 数据持久化存储
- 支持事务和外键约束
- 优化的索引设计

### 生产环境建议

- 使用专用的 MySQL 服务器
- 配置 Redis 作为缓存
- 设置真实的 SMTP/IMAP 邮件服务
- 配置 SSL/TLS 加密连接
- 定期数据库备份

---

*本项目提供完整的邮箱系统后端API，支持用户认证、邮件管理等核心功能。*
