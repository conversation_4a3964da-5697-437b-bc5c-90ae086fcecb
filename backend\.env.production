# 生产环境配置
NODE_ENV=production
PORT=5000

# 前端URL
FRONTEND_URL=http://blindedby.love

# 数据库配置
DATABASE_URL=***************************************/emaildb
REDIS_URL=redis://prod-redis:6379

# JWT配置
JWT_SECRET=your-super-secure-production-jwt-secret-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 邮件服务配置
# TODO: 替换为真实的域名和邮件服务器配置
SMTP_HOST=smtp.blindedby.love      # TODO: 替换为真实的SMTP服务器地址
SMTP_PORT=587
SMTP_USER=<EMAIL>   # TODO: 替换为真实的发送邮箱地址
SMTP_PASS=HOUsc@0202        # TODO: 替换为真实的邮箱密码

# IMAP配置
# TODO: 替换为真实的IMAP服务器配置
IMAP_HOST=imap.blindedby.love      # TODO: 替换为真实的IMAP服务器地址
IMAP_PORT=993
IMAP_USER=<EMAIL>    # TODO: 替换为真实的系统邮箱地址
IMAP_PASS=HOUsc@0202        # TODO: 替换为真实的邮箱密码

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads
STATIC_PATH=/app/static

# 安全配置
CORS_ORIGIN=https://blindedby.love,https://www.blindedby.love
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
BCRYPT_ROUNDS=12

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/app/logs/app.log

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_PATH=/health

# 缓存配置
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# 邮件处理配置
EMAIL_BATCH_SIZE=50
EMAIL_PROCESS_INTERVAL=30000
EMAIL_RETRY_ATTEMPTS=3

# 安全头配置
HELMET_ENABLED=true
CSP_ENABLED=true

# 压缩配置
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# SSL配置
SSL_CERT_PATH=/app/certs/cert.pem
SSL_KEY_PATH=/app/certs/key.pem
