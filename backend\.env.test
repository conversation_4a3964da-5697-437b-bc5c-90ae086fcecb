# 测试环境配置
NODE_ENV=test
PORT=3002

# JWT配置
JWT_SECRET=test-jwt-secret-key-for-testing-only-do-not-use-in-production
JWT_REFRESH_SECRET=test-refresh-secret-key-for-testing-only
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# 密码加密配置（降低轮数以提高测试速度）
BCRYPT_ROUNDS=4

# 测试数据库配置
DB_HOST=blindedby.love
DB_PORT=3306
DB_USER=mailadmin
DB_PASSWORD=HOUsc@0202
DB_NAME=mailserver
DB_CONNECTION_LIMIT=5
DB_ACQUIRE_TIMEOUT=30000
DB_TIMEOUT=30000

# 请求限制配置（测试环境放宽限制）
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=1000

# 前端URL
FRONTEND_URL=http://localhost:5173

# 日志级别
LOG_LEVEL=error
