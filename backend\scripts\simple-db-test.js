#!/usr/bin/env node

/**
 * 简单的数据库连接测试脚本
 * 用于快速验证 MySQL 数据库配置是否正确
 */

const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.join(__dirname, '../.env') });

// 数据库配置
const config = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'email_system',
};

async function simpleTest() {
  console.log('🔍 开始简单数据库连接测试...\n');
  
  console.log('📋 配置信息:');
  console.log(`   主机: ${config.host}:${config.port}`);
  console.log(`   用户: ${config.user}`);
  console.log(`   数据库: ${config.database}\n`);

  let connection;

  try {
    // 1. 测试基本连接
    console.log('1. 测试连接到 MySQL 服务器...');
    connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
    });
    console.log('   ✅ 连接成功\n');

    // 2. 测试数据库访问
    console.log('2. 测试数据库访问...');
    await connection.execute(`USE ${config.database}`);
    console.log('   ✅ 数据库访问成功\n');

    // 3. 简单查询测试
    console.log('3. 测试基本查询...');
    const [result] = await connection.execute('SELECT 1 as test');
    console.log(`   ✅ 查询成功: ${result[0].test}\n`);

    console.log('🎉 所有测试通过！数据库连接正常。');
    return true;

  } catch (error) {
    console.log('\n❌ 测试失败:');
    console.log(`错误: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 建议:');
      console.log('   - 检查 MySQL 服务是否运行');
      console.log('   - 验证主机和端口配置');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 建议:');
      console.log('   - 检查用户名和密码');
      console.log('   - 验证用户权限');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.log('\n💡 建议:');
      console.log('   - 检查数据库名称');
      console.log('   - 先创建数据库:');
      console.log(`     CREATE DATABASE ${config.database};`);
    }
    
    return false;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行测试
if (require.main === module) {
  simpleTest()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = simpleTest;
