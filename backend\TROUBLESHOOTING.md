# 故障排除指南

## 🔍 常见问题解决方案

### 1. SQL 语法错误

#### 问题描述
```
You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near '?' at line 1
```

#### 原因分析
- 在 `SHOW` 语句中使用了参数化查询
- MySQL 的某些语句不支持参数化查询
- 复杂的 JSON 聚合查询在某些 MySQL 版本中不被支持

#### 解决方案
✅ **已修复**：
1. 修改了测试脚本中的 `SHOW DATABASES LIKE ?` 查询
2. 简化了 `getEmailById` 和 `getEmailsByUserId` 方法中的复杂 JSON 查询
3. 使用分离的查询来获取邮件和附件数据

### 2. 数据库连接问题

#### 问题类型

##### `ECONNREFUSED` 错误
**原因**：MySQL 服务未运行或端口配置错误

**解决方案**：
```bash
# Windows
net start mysql

# macOS
brew services start mysql

# Ubuntu/Debian
sudo systemctl start mysql
```

##### `ER_ACCESS_DENIED_ERROR` 错误
**原因**：用户名、密码错误或权限不足

**解决方案**：
```sql
-- 检查用户权限
SHOW GRANTS FOR 'root'@'localhost';

-- 授予权限
GRANT ALL PRIVILEGES ON email_system.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

##### `ER_BAD_DB_ERROR` 错误
**原因**：数据库不存在

**解决方案**：
```sql
CREATE DATABASE email_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 环境配置问题

#### `.env` 文件配置
确保 `.env` 文件包含正确的配置：

```env
# 必需配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your-actual-password
DB_NAME=email_system

# JWT 配置
JWT_SECRET=your-secret-key
```

#### 常见配置错误
- ❌ 密码包含特殊字符但未正确转义
- ❌ 数据库名称拼写错误
- ❌ 端口号配置错误

### 4. MySQL 版本兼容性

#### 支持的版本
- ✅ MySQL 5.7+
- ✅ MySQL 8.0+
- ✅ MariaDB 10.2+

#### 版本检查
```sql
SELECT VERSION();
```

#### 不支持的功能
- MySQL 5.6 及以下版本不支持 JSON 数据类型
- 某些版本的 JSON 函数语法不同

### 5. 测试和验证

#### 快速连接测试
```bash
npm run db:test:simple
```

#### 完整数据库测试
```bash
npm run db:test
```

#### 手动测试连接
```bash
mysql -h localhost -u root -p -e "SELECT 1;"
```

### 6. 性能问题

#### 连接池配置
在 `.env` 文件中调整：
```env
DB_CONNECTION_LIMIT=10
DB_ACQUIRE_TIMEOUT=60000
DB_TIMEOUT=60000
```

#### 查询优化
- 确保所有外键都有索引
- 避免在大表上进行全表扫描
- 使用 `EXPLAIN` 分析查询计划

### 7. 开发环境问题

#### 端口冲突
```bash
# 检查端口占用
netstat -an | findstr :3001

# 修改端口
# 在 .env 文件中设置 PORT=3002
```

#### 权限问题
```bash
# 确保有写入日志文件的权限
chmod 755 logs/
```

### 8. 生产环境注意事项

#### 安全配置
- 使用专用数据库用户，不要使用 root
- 配置 SSL 连接
- 限制数据库访问 IP

#### 性能监控
- 启用慢查询日志
- 监控连接池使用情况
- 定期分析数据库性能

### 9. 日志分析

#### 应用程序日志
```bash
# 查看错误日志
tail -f logs/error.log

# 查看所有日志
tail -f logs/combined.log
```

#### MySQL 日志
```bash
# 查看 MySQL 错误日志
# 位置因系统而异，常见位置：
# /var/log/mysql/error.log
# /usr/local/mysql/data/hostname.err
```

### 10. 数据恢复

#### 备份数据库
```bash
mysqldump -u root -p email_system > backup.sql
```

#### 恢复数据库
```bash
mysql -u root -p email_system < backup.sql
```

## 🆘 获取更多帮助

### 检查清单
- [ ] MySQL 服务正在运行
- [ ] 数据库已创建
- [ ] `.env` 文件配置正确
- [ ] 用户权限充足
- [ ] 网络连接正常
- [ ] 防火墙允许连接

### 联系支持
如果问题仍然存在：
1. 运行 `npm run db:test:simple` 获取详细错误信息
2. 检查应用程序日志
3. 查看 MySQL 错误日志
4. 提供完整的错误消息和环境信息

---

*最后更新：2025-06-14*
