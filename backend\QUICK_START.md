# 快速启动指南

## 🚀 5分钟快速启动

### 1. 前置要求
- ✅ Node.js 18+
- ✅ MySQL 5.7+
- ✅ npm 或 yarn

### 2. 安装 MySQL（如果尚未安装）

#### Windows
```bash
# 下载并安装 MySQL Community Server
# 或使用 XAMPP
```

#### macOS
```bash
brew install mysql
brew services start mysql
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
```

### 3. 创建数据库
```bash
# 登录 MySQL
mysql -u root -p

# 创建数据库
CREATE DATABASE email_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
EXIT;
```

### 4. 克隆并配置项目
```bash
# 进入后端目录
cd backend

# 安装依赖
npm install

# 复制环境变量文件
cp .env.example .env
```

### 5. 配置环境变量
编辑 `.env` 文件：
```env
# 数据库配置（必须修改）
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your-mysql-password  # 修改为你的 MySQL 密码
DB_NAME=email_system

# JWT 密钥（建议修改）
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# 其他配置（可选）
PORT=3001
NODE_ENV=development
```

### 6. 初始化数据库
```bash
# 方法一：使用 SQL 脚本（推荐）
npm run db:init

# 方法二：自动初始化（启动时）
npm run dev
```

### 7. 启动服务
```bash
npm run dev
```

### 8. 验证安装
访问 http://localhost:3001/health，应该看到：
```json
{
  "success": true,
  "message": "服务运行正常",
  "timestamp": "2025-06-14T...",
  "version": "1.0.0"
}
```

## 🧪 测试默认账户

系统预置了测试账户：
- **邮箱**: <EMAIL>
- **密码**: 123456

## 📝 常用命令

```bash
# 开发模式启动
npm run dev

# 构建生产版本
npm run build

# 启动生产服务
npm start

# 运行测试
npm test

# 初始化数据库
npm run db:init

# 初始化测试数据库
npm run db:test:init
```

## 🔧 故障排除

### 快速测试数据库连接
```bash
# 简单连接测试
npm run db:test:simple

# 完整数据库测试
npm run db:test
```

### 数据库连接失败
1. **检查 MySQL 服务是否运行**
   ```bash
   # Windows
   net start mysql

   # macOS/Linux
   sudo systemctl start mysql
   # 或
   brew services start mysql
   ```

2. **验证 `.env` 文件中的数据库配置**
   - 确保 `DB_PASSWORD` 正确
   - 检查 `DB_HOST` 和 `DB_PORT`
   - 确认 `DB_NAME` 数据库已创建

3. **确认数据库用户权限**
   ```sql
   GRANT ALL PRIVILEGES ON email_system.* TO 'root'@'localhost';
   FLUSH PRIVILEGES;
   ```

4. **检查防火墙设置**
   - 确保 3306 端口未被阻止

### 常见错误解决

#### `ECONNREFUSED` 错误
- MySQL 服务未运行
- 端口配置错误

#### `ER_ACCESS_DENIED_ERROR` 错误
- 用户名或密码错误
- 用户权限不足

#### `ER_BAD_DB_ERROR` 错误
- 数据库不存在，需要先创建：
  ```sql
  CREATE DATABASE email_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
  ```

#### SQL 语法错误
- MySQL 版本过低（需要 5.7+）
- 字符集配置问题

### 端口冲突
如果 3001 端口被占用，修改 `.env` 文件中的 `PORT` 值。

## 📊 API 测试

### 使用 curl 测试
```bash
# 健康检查
curl http://localhost:3001/health

# 用户登录
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"123456"}'

# 获取邮件列表（需要 token）
curl -X GET http://localhost:3001/api/emails \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### 使用 Postman
1. 导入 API 集合（如果有）
2. 设置环境变量：
   - `baseUrl`: http://localhost:3001
   - `token`: 登录后获取的 JWT token

## 🔗 相关文档

- [完整设置指南](./DATABASE_SETUP.md)
- [迁移总结](./MIGRATION_SUMMARY.md)
- [API 文档](./README.md#-api文档)

## 🆘 获取帮助

如果遇到问题：
1. 检查应用程序日志：`logs/combined.log`
2. 检查错误日志：`logs/error.log`
3. 查看 MySQL 错误日志
4. 确认环境变量配置

---

*现在你的邮箱系统后端已经运行在 MySQL 数据库上了！🎉*
