# 开发环境配置
NODE_ENV=development
PORT=5000

# 前端URL
FRONTEND_URL=http://localhost:3000

# 数据库配置（开发环境使用内存数据库）
DATABASE_URL=https://blindedby.love
REDIS_URL=redis://localhost:6379

# JWT配置
JWT_SECRET=dev-jwt-secret-key-not-for-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 邮件服务配置（开发环境）
# TODO: 配置真实的SMTP服务器信息
SMTP_HOST=blindedby.love
SMTP_PORT=587
SMTP_USER=<EMAIL>  # TODO: 替换为真实的邮箱地址
SMTP_PASS=123@qwe           # TODO: 替换为真实的邮箱密码或应用密码

# IMAP配置（开发环境）
# TODO: 配置真实的IMAP服务器信息
IMAP_HOST=blindedby.love
IMAP_PORT=993
IMAP_USER=<EMAIL>  # TODO: 替换为真实的邮箱地址
IMAP_PASS=123@qwe           # TODO: 替换为真实的邮箱密码或应用密码

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
STATIC_PATH=./static

# 安全配置（开发环境宽松）
CORS_ORIGIN=http://localhost:3000,http://localhost:5173,http://127.0.0.1:3000
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=1000
BCRYPT_ROUNDS=10

# 日志配置
LOG_LEVEL=debug
LOG_FORMAT=dev
LOG_FILE=./logs/dev.log

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_PATH=/health

# 缓存配置
CACHE_TTL=300
CACHE_MAX_SIZE=100

# 邮件处理配置
EMAIL_BATCH_SIZE=10
EMAIL_PROCESS_INTERVAL=5000
EMAIL_RETRY_ATTEMPTS=3

# 开发模式特殊配置
ENABLE_MOCK_DATA=true
ENABLE_DEBUG_ROUTES=true
DISABLE_AUTH_FOR_TESTING=false
