import { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import { mysqlDB } from '../database/mysqlDatabase';
import { generateTokens } from '../middleware/auth';
import { AppError, LoginRequest, CreateUserRequest, ApiResponse, User } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

// 用户注册
export const register = asyncHandler(async (req: Request, res: Response) => {
  const { email, username, displayName, password }: CreateUserRequest = req.body;

  // 验证输入
  if (!email || !username || !displayName || !password) {
    throw new AppError('所有字段都是必填的', 400);
  }

  // 验证邮箱格式
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new AppError('邮箱格式无效', 400);
  }

  // 验证密码长度
  if (password.length < 6) {
    throw new AppError('密码至少需要6位', 400);
  }

  // 检查邮箱是否已存在
  const existingUserByEmail = await mysqlDB.getUserByEmail(email);
  if (existingUserByEmail) {
    throw new AppError('该邮箱已被注册', 409);
  }

  // 检查用户名是否已存在
  const existingUserByUsername = await mysqlDB.getUserByUsername(username);
  if (existingUserByUsername) {
    throw new AppError('该用户名已被使用', 409);
  }

  // 加密密码
  const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
  const passwordHash = await bcrypt.hash(password, saltRounds);

  // 创建用户
  const newUser = await mysqlDB.createUser({
    email,
    username,
    displayName,
    passwordHash,
    isActive: true,
  });

  // 生成tokens
  const { accessToken, refreshToken } = generateTokens({
    id: newUser.id,
    email: newUser.email,
    username: newUser.username,
  });

  // 返回用户信息（不包含密码）
  const userResponse = {
    id: newUser.id,
    email: newUser.email,
    username: newUser.username,
    displayName: newUser.displayName,
    avatarUrl: newUser.avatarUrl,
    createdAt: newUser.createdAt,
    updatedAt: newUser.updatedAt,
    isActive: newUser.isActive,
  };

  logger.info('User registered successfully', { userId: newUser.id, email: newUser.email });

  const response: ApiResponse<{
    user: Omit<User, 'passwordHash'>;
    token: string;
    refreshToken: string;
  }> = {
    success: true,
    data: {
      user: userResponse,
      token: accessToken,
      refreshToken,
    },
    message: '注册成功',
  };

  res.status(201).json(response);
});

// 用户登录
export const login = asyncHandler(async (req: Request, res: Response) => {
  const { email, password }: LoginRequest = req.body;

  // 验证输入
  if (!email || !password) {
    throw new AppError('邮箱和密码都是必填的', 400);
  }

  // 查找用户
  const user = await mysqlDB.getUserByEmail(email);
  if (!user) {
    throw new AppError('邮箱或密码错误', 401);
  }

  // 检查用户是否激活
  if (!user.isActive) {
    throw new AppError('账户已被禁用', 401);
  }

  // 验证密码
  const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
  if (!isPasswordValid) {
    throw new AppError('邮箱或密码错误', 401);
  }

  // 生成tokens
  const { accessToken, refreshToken } = generateTokens({
    id: user.id,
    email: user.email,
    username: user.username,
  });

  // 返回用户信息（不包含密码）
  const userResponse = {
    id: user.id,
    email: user.email,
    username: user.username,
    displayName: user.displayName,
    avatarUrl: user.avatarUrl,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
    isActive: user.isActive,
  };

  logger.info('User logged in successfully', { userId: user.id, email: user.email });

  // 登录成功后，可以启动邮件同步（这里暂时注释掉）
  // imapService.startPolling(user.id);

  const response: ApiResponse<{
    user: Omit<User, 'passwordHash'>;
    token: string;
    refreshToken: string;
  }> = {
    success: true,
    data: {
      user: userResponse,
      token: accessToken,
      refreshToken,
    },
    message: '登录成功',
  };

  res.json(response);
});

// 获取当前用户信息
export const getCurrentUser = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AppError('用户未认证', 401);
  }

  // 返回用户信息（不包含密码）
  const userResponse = {
    id: req.user.id,
    email: req.user.email,
    username: req.user.username,
    displayName: req.user.displayName,
    avatarUrl: req.user.avatarUrl,
    createdAt: req.user.createdAt,
    updatedAt: req.user.updatedAt,
    isActive: req.user.isActive,
  };

  const response: ApiResponse<Omit<User, 'passwordHash'>> = {
    success: true,
    data: userResponse,
    message: '获取用户信息成功',
  };

  res.json(response);
});

// 刷新token
export const refreshToken = asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken: token } = req.body;

  if (!token) {
    throw new AppError('刷新令牌缺失', 400);
  }

  // 这里应该验证refresh token，为简化起见，暂时跳过
  // 在实际应用中，应该将refresh token存储在数据库中并验证

  const response: ApiResponse<{ token: string }> = {
    success: true,
    data: {
      token: 'new-access-token', // 这里应该生成新的access token
    },
    message: '令牌刷新成功',
  };

  res.json(response);
});

// 用户登出
export const logout = asyncHandler(async (req: Request, res: Response) => {
  // 在实际应用中，这里应该将token加入黑名单或从数据库中删除refresh token
  
  logger.info('User logged out', { userId: req.user?.id });

  const response: ApiResponse<null> = {
    success: true,
    data: null,
    message: '登出成功',
  };

  res.json(response);
});

