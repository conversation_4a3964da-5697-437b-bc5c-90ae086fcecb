import request from 'supertest';
import app from '../src/app';
import { TestHelper } from './helpers/testUtils';

describe('Authentication API', () => {
  beforeEach(async () => {
    await TestHelper.cleanupTestData();
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'newuser',
        displayName: '新用户',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      TestHelper.validateApiResponse(response, 201);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('refreshToken');
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user.username).toBe(userData.username);
      expect(response.body.data.user).not.toHaveProperty('passwordHash');
    });

    it('should fail with missing required fields', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>',
          // missing username, displayName, password
        });

      TestHelper.validateErrorResponse(response, 400);
    });

    it('should fail with duplicate email', async () => {
      const userData = {
        email: '<EMAIL>', // 已存在的邮箱
        username: 'newuser',
        displayName: '新用户',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      TestHelper.validateErrorResponse(response, 409);
    });

    it('should fail with invalid email format', async () => {
      const userData = {
        email: 'invalid-email',
        username: 'newuser',
        displayName: '新用户',
        password: 'password123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);

      TestHelper.validateErrorResponse(response, 400);
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: '123456',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('refreshToken');
      expect(response.body.data.user.email).toBe(loginData.email);
    });

    it('should fail with invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: '123456',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      TestHelper.validateErrorResponse(response, 401);
    });

    it('should fail with invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      TestHelper.validateErrorResponse(response, 401);
    });

    it('should fail with missing credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({});

      TestHelper.validateErrorResponse(response, 400);
    });
  });

  describe('GET /api/auth/me', () => {
    it('should get current user with valid token', async () => {
      const { token } = await TestHelper.createTestUser();

      const response = await request(app)
        .get('/api/auth/me')
        .set(TestHelper.getAuthHeader(token));

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('email');
      expect(response.body.data).toHaveProperty('username');
      expect(response.body.data).not.toHaveProperty('passwordHash');
    });

    it('should fail without token', async () => {
      const response = await request(app)
        .get('/api/auth/me');

      TestHelper.validateErrorResponse(response, 401);
    });

    it('should fail with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token');

      TestHelper.validateErrorResponse(response, 401);
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully with valid token', async () => {
      const { token } = await TestHelper.createTestUser();

      const response = await request(app)
        .post('/api/auth/logout')
        .set(TestHelper.getAuthHeader(token));

      TestHelper.validateApiResponse(response, 200);
    });

    it('should fail without token', async () => {
      const response = await request(app)
        .post('/api/auth/logout');

      TestHelper.validateErrorResponse(response, 401);
    });
  });

  describe('POST /api/auth/refresh', () => {
    it('should refresh token successfully', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: 'some-refresh-token' });

      TestHelper.validateApiResponse(response, 200);
      expect(response.body.data).toHaveProperty('token');
    });

    it('should fail without refresh token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh')
        .send({});

      TestHelper.validateErrorResponse(response, 400);
    });
  });
});
