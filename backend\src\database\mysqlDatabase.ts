import { RowDataPacket, ResultSetHeader } from 'mysql2/promise';
import { getPool } from '../config/database';
import { User, Email, Folder, Contact, Attachment } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';

// MySQL 数据库服务类
export class MySQLDatabase {
  private pool = getPool();

  // 用户相关方法
  async createUser(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
    const id = uuidv4();
    const now = new Date();
    
    const query = `
      INSERT INTO users (id, email, username, display_name, avatar_url, password_hash, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    try {
      await this.pool.execute(query, [
        id,
        userData.email,
        userData.username,
        userData.displayName,
        userData.avatarUrl || null,
        userData.passwordHash,
        userData.isActive,
        now,
        now
      ]);

      const user: User = {
        id,
        ...userData,
        createdAt: now,
        updatedAt: now,
      };

      logger.info('User created successfully', { userId: id, email: userData.email });
      return user;
    } catch (error) {
      logger.error('Failed to create user:', error);
      throw error;
    }
  }

  async getUserById(id: string): Promise<User | undefined> {
    const query = 'SELECT * FROM users WHERE id = ?';
    
    try {
      const [rows] = await this.pool.execute<RowDataPacket[]>(query, [id]);
      
      if (rows.length === 0) {
        return undefined;
      }

      return this.mapRowToUser(rows[0]);
    } catch (error) {
      logger.error('Failed to get user by id:', error);
      throw error;
    }
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const query = 'SELECT * FROM users WHERE email = ?';
    
    try {
      const [rows] = await this.pool.execute<RowDataPacket[]>(query, [email]);
      
      if (rows.length === 0) {
        return undefined;
      }

      return this.mapRowToUser(rows[0]);
    } catch (error) {
      logger.error('Failed to get user by email:', error);
      throw error;
    }
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const query = 'SELECT * FROM users WHERE username = ?';
    
    try {
      const [rows] = await this.pool.execute<RowDataPacket[]>(query, [username]);
      
      if (rows.length === 0) {
        return undefined;
      }

      return this.mapRowToUser(rows[0]);
    } catch (error) {
      logger.error('Failed to get user by username:', error);
      throw error;
    }
  }

  // 邮件相关方法
  async createEmail(emailData: Omit<Email, 'id' | 'createdAt' | 'updatedAt'>): Promise<Email> {
    const id = uuidv4();
    const now = new Date();
    
    const query = `
      INSERT INTO emails (
        id, message_id, user_id, folder_id, subject, sender_email, sender_name,
        recipients, cc_recipients, bcc_recipients, content_text, content_html,
        is_read, is_starred, is_deleted, received_at, sent_at, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    try {
      await this.pool.execute(query, [
        id,
        emailData.messageId,
        emailData.userId,
        emailData.folderId,
        emailData.subject,
        emailData.senderEmail,
        emailData.senderName,
        JSON.stringify(emailData.recipients),
        JSON.stringify(emailData.ccRecipients || []),
        JSON.stringify(emailData.bccRecipients || []),
        emailData.contentText,
        emailData.contentHtml,
        emailData.isRead,
        emailData.isStarred,
        emailData.isDeleted,
        emailData.receivedAt || null,
        emailData.sentAt || null,
        now,
        now
      ]);

      // 创建附件记录
      if (emailData.attachments && emailData.attachments.length > 0) {
        await this.createAttachments(id, emailData.attachments);
      }

      const email: Email = {
        id,
        ...emailData,
        createdAt: now,
        updatedAt: now,
      };

      logger.info('Email created successfully', { emailId: id, userId: emailData.userId });
      return email;
    } catch (error) {
      logger.error('Failed to create email:', error);
      throw error;
    }
  }

  async getEmailById(id: string): Promise<Email | undefined> {
    const query = `
      SELECT e.*, 
        COALESCE(
          JSON_ARRAYAGG(
            CASE WHEN a.id IS NOT NULL THEN
              JSON_OBJECT(
                'id', a.id,
                'filename', a.filename,
                'contentType', a.content_type,
                'size', a.size,
                'url', a.url
              )
            END
          ), 
          JSON_ARRAY()
        ) as attachments
      FROM emails e
      LEFT JOIN attachments a ON e.id = a.email_id
      WHERE e.id = ?
      GROUP BY e.id
    `;
    
    try {
      const [rows] = await this.pool.execute<RowDataPacket[]>(query, [id]);
      
      if (rows.length === 0) {
        return undefined;
      }

      return this.mapRowToEmail(rows[0]);
    } catch (error) {
      logger.error('Failed to get email by id:', error);
      throw error;
    }
  }

  async getEmailsByUserId(userId: string, folderId?: string): Promise<Email[]> {
    let query = `
      SELECT e.*, 
        COALESCE(
          JSON_ARRAYAGG(
            CASE WHEN a.id IS NOT NULL THEN
              JSON_OBJECT(
                'id', a.id,
                'filename', a.filename,
                'contentType', a.content_type,
                'size', a.size,
                'url', a.url
              )
            END
          ), 
          JSON_ARRAY()
        ) as attachments
      FROM emails e
      LEFT JOIN attachments a ON e.id = a.email_id
      WHERE e.user_id = ? AND e.is_deleted = FALSE
    `;
    
    const params: any[] = [userId];
    
    if (folderId) {
      query += ' AND e.folder_id = ?';
      params.push(folderId);
    }
    
    query += ' GROUP BY e.id ORDER BY e.created_at DESC';
    
    try {
      const [rows] = await this.pool.execute<RowDataPacket[]>(query, params);
      return rows.map(row => this.mapRowToEmail(row));
    } catch (error) {
      logger.error('Failed to get emails by user id:', error);
      throw error;
    }
  }

  async updateEmail(id: string, updates: Partial<Email>): Promise<Email | undefined> {
    const setClause: string[] = [];
    const params: any[] = [];
    
    // 构建动态更新语句
    Object.entries(updates).forEach(([key, value]) => {
      if (key === 'id' || key === 'createdAt') return; // 不允许更新这些字段
      
      const dbField = this.camelToSnake(key);
      if (key === 'recipients' || key === 'ccRecipients' || key === 'bccRecipients') {
        setClause.push(`${dbField} = ?`);
        params.push(JSON.stringify(value));
      } else {
        setClause.push(`${dbField} = ?`);
        params.push(value);
      }
    });
    
    if (setClause.length === 0) {
      return this.getEmailById(id);
    }
    
    setClause.push('updated_at = ?');
    params.push(new Date());
    params.push(id);
    
    const query = `UPDATE emails SET ${setClause.join(', ')} WHERE id = ?`;
    
    try {
      const [result] = await this.pool.execute<ResultSetHeader>(query, params);
      
      if (result.affectedRows === 0) {
        return undefined;
      }
      
      return this.getEmailById(id);
    } catch (error) {
      logger.error('Failed to update email:', error);
      throw error;
    }
  }

  // 文件夹相关方法
  async getFoldersByUserId(userId: string): Promise<Folder[]> {
    const query = 'SELECT * FROM folders WHERE user_id = ? ORDER BY created_at ASC';
    
    try {
      const [rows] = await this.pool.execute<RowDataPacket[]>(query, [userId]);
      return rows.map(row => this.mapRowToFolder(row));
    } catch (error) {
      logger.error('Failed to get folders by user id:', error);
      throw error;
    }
  }

  async getFolderById(id: string): Promise<Folder | undefined> {
    const query = 'SELECT * FROM folders WHERE id = ?';
    
    try {
      const [rows] = await this.pool.execute<RowDataPacket[]>(query, [id]);
      
      if (rows.length === 0) {
        return undefined;
      }

      return this.mapRowToFolder(rows[0]);
    } catch (error) {
      logger.error('Failed to get folder by id:', error);
      throw error;
    }
  }

  // 联系人相关方法
  async getContactsByUserId(userId: string): Promise<Contact[]> {
    const query = 'SELECT * FROM contacts WHERE user_id = ? ORDER BY name ASC';
    
    try {
      const [rows] = await this.pool.execute<RowDataPacket[]>(query, [userId]);
      return rows.map(row => this.mapRowToContact(row));
    } catch (error) {
      logger.error('Failed to get contacts by user id:', error);
      throw error;
    }
  }

  // 附件相关方法
  private async createAttachments(emailId: string, attachments: Attachment[]): Promise<void> {
    if (attachments.length === 0) return;
    
    const query = `
      INSERT INTO attachments (id, email_id, filename, content_type, size, url)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    try {
      for (const attachment of attachments) {
        await this.pool.execute(query, [
          attachment.id,
          emailId,
          attachment.filename,
          attachment.contentType,
          attachment.size,
          attachment.url
        ]);
      }
    } catch (error) {
      logger.error('Failed to create attachments:', error);
      throw error;
    }
  }

  // 辅助方法：将数据库行映射为用户对象
  private mapRowToUser(row: RowDataPacket): User {
    return {
      id: row.id,
      email: row.email,
      username: row.username,
      displayName: row.display_name,
      avatarUrl: row.avatar_url,
      passwordHash: row.password_hash,
      isActive: Boolean(row.is_active),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  // 辅助方法：将数据库行映射为邮件对象
  private mapRowToEmail(row: RowDataPacket): Email {
    return {
      id: row.id,
      messageId: row.message_id,
      userId: row.user_id,
      folderId: row.folder_id,
      subject: row.subject,
      senderEmail: row.sender_email,
      senderName: row.sender_name,
      recipients: JSON.parse(row.recipients),
      ccRecipients: row.cc_recipients ? JSON.parse(row.cc_recipients) : [],
      bccRecipients: row.bcc_recipients ? JSON.parse(row.bcc_recipients) : [],
      contentText: row.content_text,
      contentHtml: row.content_html,
      attachments: row.attachments ? JSON.parse(row.attachments).filter((a: any) => a !== null) : [],
      isRead: Boolean(row.is_read),
      isStarred: Boolean(row.is_starred),
      isDeleted: Boolean(row.is_deleted),
      receivedAt: row.received_at ? new Date(row.received_at) : undefined,
      sentAt: row.sent_at ? new Date(row.sent_at) : undefined,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  // 辅助方法：将数据库行映射为文件夹对象
  private mapRowToFolder(row: RowDataPacket): Folder {
    return {
      id: row.id,
      userId: row.user_id,
      name: row.name,
      type: row.type,
      parentId: row.parent_id,
      emailCount: row.email_count,
      unreadCount: row.unread_count,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  // 辅助方法：将数据库行映射为联系人对象
  private mapRowToContact(row: RowDataPacket): Contact {
    return {
      id: row.id,
      userId: row.user_id,
      name: row.name,
      email: row.email,
      phone: row.phone,
      company: row.company,
      notes: row.notes,
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at),
    };
  }

  // 辅助方法：将驼峰命名转换为下划线命名
  private camelToSnake(str: string): string {
    return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
  }
}

// 导出单例实例
export const mysqlDB = new MySQLDatabase();
