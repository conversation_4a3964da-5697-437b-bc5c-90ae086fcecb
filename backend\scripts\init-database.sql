-- 邮箱系统数据库初始化脚本
-- 使用前请先创建数据库: CREATE DATABASE email_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS mailserver CHARACTER
SET
  utf8mb4 COLLATE utf8mb4_unicode_ci;

USE mailserver;

-- 创建域名表 (先创建被引用的表)
CREATE TABLE IF NOT EXISTS domains (
  id VARCHAR(36) PRIMARY KEY,
  domain VARCHAR(255) NOT NULL COMMENT '域名',
  description VARCHAR(500) DEFAULT NULL COMMENT '域名描述',
  status ENUM('active','inactive') DEFAULT 'active' COMMENT '域名状态',
  max_users INT DEFAULT 0 COMMENT '最大用户数，0表示无限制',
  current_users INT DEFAULT 0 COMMENT '当前用户数',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_domain (domain),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件域名表';

-- 插入默认域名
INSERT IGNORE INTO domains (id, domain, description, status) VALUES
('domain-1', 'blindedby.love', '示例域名', 'active'),
('domain-2', 'localhost', '本地测试域名', 'active');

-- 创建用户表 (添加外键约束)
CREATE TABLE IF NOT EXISTS users (
  id VARCHAR(36) PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  domain_id VARCHAR(36) NOT NULL,
  username VARCHAR(100) UNIQUE NOT NULL,
  display_name VARCHAR(255) NOT NULL,
  avatar_url VARCHAR(500),
  password_hash VARCHAR(255) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_email (email),
  INDEX idx_username (username),
  INDEX idx_domain_id (domain_id),
  CONSTRAINT fk_users_domain FOREIGN KEY (domain_id) REFERENCES domains(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例用户数据
INSERT IGNORE INTO users (id, email, domain_id, username, display_name, password_hash, is_active) 
VALUES ('user-1', '<EMAIL>', 'domain-1', 'testuser', '测试用户', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL/.HL9S6', TRUE);

-- 创建文件夹表 (保持不变)
CREATE TABLE IF NOT EXISTS folders (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  name VARCHAR(255) NOT NULL,
  type ENUM('inbox', 'sent', 'draft', 'trash', 'custom') NOT NULL,
  parent_id VARCHAR(36),
  email_count INT DEFAULT 0,
  unread_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (parent_id) REFERENCES folders(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_type (type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认文件夹 (确保用户已存在)
INSERT IGNORE INTO folders (id, user_id, name, type, email_count, unread_count) VALUES
('folder-1', 'user-1', '收件箱', 'inbox', 3, 2),
('folder-2', 'user-1', '已发送', 'sent', 1, 0),
('folder-3', 'user-1', '草稿箱', 'draft', 1, 0),
('folder-4', 'user-1', '垃圾箱', 'trash', 0, 0);

-- 创建邮件表
CREATE TABLE IF NOT EXISTS emails (
  id VARCHAR(36) PRIMARY KEY,
  message_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(36) NOT NULL,
  folder_id VARCHAR(36) NOT NULL,
  subject TEXT,
  sender_email VARCHAR(255) NOT NULL,
  sender_name VARCHAR(255),
  recipients JSON NOT NULL,
  cc_recipients JSON,
  bcc_recipients JSON,
  content_text LONGTEXT,
  content_html LONGTEXT,
  is_read BOOLEAN DEFAULT FALSE,
  is_starred BOOLEAN DEFAULT FALSE,
  is_deleted BOOLEAN DEFAULT FALSE,
  received_at TIMESTAMP NULL,
  sent_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_folder_id (folder_id),
  INDEX idx_sender_email (sender_email),
  INDEX idx_is_read (is_read),
  INDEX idx_is_starred (is_starred),
  INDEX idx_is_deleted (is_deleted),
  INDEX idx_received_at (received_at),
  INDEX idx_sent_at (sent_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建附件表
CREATE TABLE IF NOT EXISTS attachments (
  id VARCHAR(36) PRIMARY KEY,
  email_id VARCHAR(36) NOT NULL,
  filename VARCHAR(255) NOT NULL,
  content_type VARCHAR(100),
  size BIGINT,
  url VARCHAR(500),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (email_id) REFERENCES emails(id) ON DELETE CASCADE,
  INDEX idx_email_id (email_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建联系人表
CREATE TABLE IF NOT EXISTS contacts (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36) NOT NULL,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50),
  company VARCHAR(255),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例邮件
INSERT IGNORE INTO emails (
  id, message_id, user_id, folder_id, subject, sender_email, sender_name,
  recipients, cc_recipients, bcc_recipients, content_text, content_html,
  is_read, is_starred, is_deleted, received_at, created_at, updated_at
) VALUES
('email-1', 'msg-1', 'user-1', 'inbox', '欢迎使用邮箱系统', '<EMAIL>', '系统管理员',
 '["<EMAIL>"]', '[]', '[]', 
 '欢迎使用我们的邮箱系统！这是一个功能完整的邮件管理平台。',
 '<p>欢迎使用我们的邮箱系统！这是一个功能完整的邮件管理平台。</p>',
 FALSE, TRUE, FALSE, DATE_SUB(NOW(), INTERVAL 30 MINUTE), NOW(), NOW()),

('email-2', 'msg-2', 'user-1', 'inbox', '项目进度更新', '<EMAIL>', '项目经理',
 '["<EMAIL>"]', '["<EMAIL>"]', '[]',
 '本周项目进度顺利，已完成主要功能开发。',
 '<p>本周项目进度顺利，已完成主要功能开发。</p><ul><li>前端界面完成</li><li>后端API开发</li><li>数据库设计</li></ul>',
 TRUE, FALSE, FALSE, DATE_SUB(NOW(), INTERVAL 2 HOUR), NOW(), NOW()),

('email-3', 'msg-3', 'user-1', 'inbox', '会议邀请：周例会', '<EMAIL>', '会议助手',
 '["<EMAIL>"]', '[]', '[]',
 '邀请您参加本周的例会，时间：周五下午2点。',
 '<p>邀请您参加本周的例会</p><p><strong>时间：</strong>周五下午2点</p><p><strong>地点：</strong>会议室A</p>',
 FALSE, FALSE, FALSE, DATE_SUB(NOW(), INTERVAL 6 HOUR), NOW(), NOW()),

('email-4', 'msg-4', 'user-1', 'sent', '回复：项目需求确认', '<EMAIL>', '测试用户',
 '["<EMAIL>"]', '[]', '[]',
 '感谢您的需求确认，我们将按照讨论的方案进行开发。',
 '<p>感谢您的需求确认，我们将按照讨论的方案进行开发。</p>',
 TRUE, FALSE, FALSE, NULL, DATE_SUB(NOW(), INTERVAL 1 DAY), NOW()),

('email-5', 'msg-5', 'user-1', 'draft', '待发送：月度总结报告', '<EMAIL>', '测试用户',
 '["<EMAIL>"]', '[]', '[]',
 '本月工作总结...',
 '<p>本月工作总结...</p>',
 TRUE, FALSE, FALSE, NULL, DATE_SUB(NOW(), INTERVAL 12 HOUR), NOW());

-- 为第二封邮件添加附件
INSERT IGNORE INTO attachments (id, email_id, filename, content_type, size, url, created_at)
VALUES ('att-1', 'email-2', '项目报告.pdf', 'application/pdf', 1024000, '/attachments/report.pdf', NOW());

-- 插入示例联系人
INSERT IGNORE INTO contacts (id, user_id, name, email, phone, company, notes, created_at, updated_at) VALUES
('contact-1', 'user-1', '张三', '<EMAIL>', '13800138000', 'ABC公司', '项目合作伙伴', NOW(), NOW()),
('contact-2', 'user-1', '李四', '<EMAIL>', '13900139000', 'XYZ公司', '技术顾问', NOW(), NOW());


