import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // 路径别名
  resolve: {
    alias: {
      "@": resolve(__dirname, "./src"),
    },
  },

  // 构建优化
  build: {
    // 目标浏览器
    target: "es2015",

    // 输出目录
    outDir: "dist",

    // 生成源码映射
    sourcemap: process.env.NODE_ENV === "development",

    // 压缩选项
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: process.env.NODE_ENV === "production",
        drop_debugger: true,
      },
    },

    // 代码分割
    rollupOptions: {
      output: {
        // 手动分割代码块
        manualChunks: {
          // React相关
          "react-vendor": ["react", "react-dom"],

          // React Query
          "query-vendor": ["@tanstack/react-query"],

          // Ant Design
          "antd-vendor": ["antd"],

          // 路由
          "router-vendor": ["react-router-dom"],

          // 状态管理
          "state-vendor": ["zustand"],

          // 工具库
          "utils-vendor": ["axios", "dayjs"],
        },

        // 文件命名
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId
                .split("/")
                .pop()
                ?.replace(/\.\w+$/, "") || "chunk"
            : "chunk";
          return `js/${facadeModuleId}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split(".") || [];
          const ext = info[info.length - 1];
          if (/\.(png|jpe?g|gif|svg|webp|ico)$/.test(assetInfo.name || "")) {
            return `images/[name]-[hash].${ext}`;
          }
          if (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name || "")) {
            return `fonts/[name]-[hash].${ext}`;
          }
          return `assets/[name]-[hash].${ext}`;
        },
      },
    },

    // 块大小警告限制
    chunkSizeWarningLimit: 500,
  },

  // 开发服务器配置
  server: {
    port: 3000,
    host: "0.0.0.0", // 允许外部访问
    strictPort: true, // 端口被占用时不自动尝试下一个端口

    // 代理API请求
    proxy: {
      "/api": {
        // target: "https://blindedby.love/api",
        target: "http://localhost:3001/api",
        changeOrigin: true,
        secure: false,
        ws: true, // 支持WebSocket
        configure: (proxy) => {
          proxy.on("error", (err) => {
            console.log("proxy error", err);
          });
          proxy.on("proxyReq", (_, req) => {
            console.log("Sending Request to the Target:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req) => {
            console.log(
              "Received Response from the Target:",
              proxyRes.statusCode,
              req.url
            );
          });
        },
      },
      // WebSocket代理
      "/ws": {
        target: "wss://blindedby.love/",
        ws: true,
        changeOrigin: true,
      },
    },

    // CORS配置
    cors: {
      origin: ["https://blindedby.love/api", "http://127.0.0.1:5000"],
      credentials: true,
    },
  },

  // 预览服务器配置
  preview: {
    port: 3000,
    host: true,
  },

  // 依赖优化
  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "react-router-dom",
      "@tanstack/react-query",
      "antd",
      "zustand",
      "axios",
    ],
    exclude: ["@vite/client", "@vite/env"],
  },

  // 环境变量
  define: {
    __DEV__: process.env.NODE_ENV === "development",
    __PROD__: process.env.NODE_ENV === "production",
  },
});
