import { Request, Response } from 'express';
import { mysqlDB } from '../database/mysqlDatabase';
import { AppError, ApiResponse, Email, PaginatedResponse, SendEmailRequest } from '../types';
import { asyncHandler } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

// 获取邮件列表
export const getEmails = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AppError('用户未认证', 401);
  }

  const { folderId, page = '1', limit = '20' } = req.query;
  const pageNum = parseInt(page as string);
  const limitNum = parseInt(limit as string);

  // 获取用户的邮件
  const emails = await mysqlDB.getEmailsByUserId(req.user.id, folderId as string);

  // 排序（按创建时间倒序）
  emails.sort((a: Email, b: Email) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  // 分页
  const startIndex = (pageNum - 1) * limitNum;
  const endIndex = startIndex + limitNum;
  const paginatedEmails = emails.slice(startIndex, endIndex);

  const response: ApiResponse<PaginatedResponse<Email>> = {
    success: true,
    data: {
      data: paginatedEmails,
      total: emails.length,
      page: pageNum,
      limit: limitNum,
      hasNext: endIndex < emails.length,
      hasPrev: pageNum > 1,
    },
    message: '获取邮件列表成功',
  };

  res.json(response);
});

// 获取单个邮件详情
export const getEmailById = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AppError('用户未认证', 401);
  }

  const { id } = req.params;
  const email = await mysqlDB.getEmailById(id);

  if (!email) {
    throw new AppError('邮件不存在', 404);
  }

  // 检查邮件是否属于当前用户
  if (email.userId !== req.user.id) {
    throw new AppError('无权访问此邮件', 403);
  }

  // 如果邮件未读，标记为已读
  if (!email.isRead) {
    await mysqlDB.updateEmail(id, { isRead: true });
    email.isRead = true;
  }

  const response: ApiResponse<Email> = {
    success: true,
    data: email,
    message: '获取邮件详情成功',
  };

  res.json(response);
});

// 发送邮件
export const sendEmail = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AppError('用户未认证', 401);
  }

  const { to, cc, bcc, subject, content }: SendEmailRequest = req.body;

  // 验证输入
  if (!to || to.length === 0) {
    throw new AppError('收件人不能为空', 400);
  }

  if (!subject) {
    throw new AppError('邮件主题不能为空', 400);
  }

  if (!content) {
    throw new AppError('邮件内容不能为空', 400);
  }

  // 创建邮件记录
  const emailData = {
    messageId: `msg-${uuidv4()}`,
    userId: req.user.id,
    folderId: 'sent', // 发送的邮件放在已发送文件夹
    subject,
    senderEmail: req.user.email,
    senderName: req.user.displayName,
    recipients: to,
    ccRecipients: cc || [],
    bccRecipients: bcc || [],
    contentText: content,
    contentHtml: content, // 简化处理，实际应该支持HTML格式
    attachments: [], // 暂时不处理附件
    isRead: true, // 发送的邮件默认已读
    isStarred: false,
    isDeleted: false,
    sentAt: new Date(),
  };

  const newEmail = await mysqlDB.createEmail(emailData);

  // 这里应该调用实际的邮件发送服务（SMTP）
  // 为了演示，我们只是记录日志
  logger.info('Email sent', {
    emailId: newEmail.id,
    from: req.user.email,
    to,
    subject,
  });

  const response: ApiResponse<Email> = {
    success: true,
    data: newEmail,
    message: '邮件发送成功',
  };

  res.status(201).json(response);
});

// 更新邮件状态
export const updateEmail = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AppError('用户未认证', 401);
  }

  const { id } = req.params;
  const updates = req.body;

  const email = await mysqlDB.getEmailById(id);
  if (!email) {
    throw new AppError('邮件不存在', 404);
  }

  // 检查邮件是否属于当前用户
  if (email.userId !== req.user.id) {
    throw new AppError('无权修改此邮件', 403);
  }

  // 只允许更新特定字段
  const allowedUpdates = ['isRead', 'isStarred', 'folderId'];
  const filteredUpdates: any = {};

  for (const key of allowedUpdates) {
    if (key in updates) {
      filteredUpdates[key] = updates[key];
    }
  }

  const updatedEmail = await mysqlDB.updateEmail(id, filteredUpdates);

  const response: ApiResponse<Email> = {
    success: true,
    data: updatedEmail!,
    message: '邮件更新成功',
  };

  res.json(response);
});

// 删除邮件
export const deleteEmail = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AppError('用户未认证', 401);
  }

  const { id } = req.params;
  const email = await mysqlDB.getEmailById(id);

  if (!email) {
    throw new AppError('邮件不存在', 404);
  }

  // 检查邮件是否属于当前用户
  if (email.userId !== req.user.id) {
    throw new AppError('无权删除此邮件', 403);
  }

  // 软删除：标记为已删除
  await mysqlDB.updateEmail(id, { isDeleted: true, folderId: 'trash' });

  logger.info('Email deleted', { emailId: id, userId: req.user.id });

  const response: ApiResponse<null> = {
    success: true,
    data: null,
    message: '邮件删除成功',
  };

  res.json(response);
});

// 搜索邮件
export const searchEmails = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user) {
    throw new AppError('用户未认证', 401);
  }

  const { query, from, subject, dateFrom, dateTo } = req.body;

  // 验证至少有一个搜索条件
  if (!query && !from && !subject && !dateFrom && !dateTo) {
    throw new AppError('请提供至少一个搜索条件', 400);
  }

  // 验证query不为空字符串
  if (query !== undefined && query.trim() === '') {
    throw new AppError('搜索关键词不能为空', 400);
  }

  // 获取用户的所有邮件
  const allEmails = await mysqlDB.getEmailsByUserId(req.user.id);

  // 简单的搜索实现
  let filteredEmails = allEmails;

  if (query) {
    const searchTerm = query.toLowerCase();
    filteredEmails = filteredEmails.filter(email =>
      email.subject.toLowerCase().includes(searchTerm) ||
      email.contentText.toLowerCase().includes(searchTerm) ||
      email.senderEmail.toLowerCase().includes(searchTerm) ||
      email.senderName.toLowerCase().includes(searchTerm)
    );
  }

  if (from) {
    filteredEmails = filteredEmails.filter(email =>
      email.senderEmail.toLowerCase().includes(from.toLowerCase())
    );
  }

  if (subject) {
    filteredEmails = filteredEmails.filter(email =>
      email.subject.toLowerCase().includes(subject.toLowerCase())
    );
  }

  if (dateFrom) {
    const fromDate = new Date(dateFrom);
    filteredEmails = filteredEmails.filter(email =>
      new Date(email.createdAt) >= fromDate
    );
  }

  if (dateTo) {
    const toDate = new Date(dateTo);
    filteredEmails = filteredEmails.filter(email =>
      new Date(email.createdAt) <= toDate
    );
  }

  // 排序
  filteredEmails.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  const response: ApiResponse<PaginatedResponse<Email>> = {
    success: true,
    data: {
      data: filteredEmails,
      total: filteredEmails.length,
      page: 1,
      limit: filteredEmails.length,
      hasNext: false,
      hasPrev: false,
    },
    message: '搜索完成',
  };

  res.json(response);
});
