import { mysqlDB } from './mysqlDatabase';
import { getPool } from '../config/database';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';

// 初始化种子数据
export const seedDatabase = async (): Promise<void> => {
  const pool = getPool();
  
  try {
    // 检查是否已有数据
    const [userRows] = await pool.execute('SELECT COUNT(*) as count FROM users');
    const userCount = (userRows as any)[0].count;
    
    if (userCount > 0) {
      logger.info('Database already has data, skipping seed');
      return;
    }

    logger.info('Starting database seeding...');

    // 创建示例用户
    const hashedPassword = await bcrypt.hash('123456', 12);
    const testUserId = 'user-1';
    
    await pool.execute(`
      INSERT INTO users (id, email, username, display_name, password_hash, is_active, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
    `, [testUserId, '<EMAIL>', 'testuser', '测试用户', hashedPassword, true]);

    // 创建默认文件夹
    const folders = [
      {
        id: 'inbox',
        userId: testUserId,
        name: '收件箱',
        type: 'inbox',
        emailCount: 3,
        unreadCount: 2,
      },
      {
        id: 'sent',
        userId: testUserId,
        name: '已发送',
        type: 'sent',
        emailCount: 1,
        unreadCount: 0,
      },
      {
        id: 'draft',
        userId: testUserId,
        name: '草稿箱',
        type: 'draft',
        emailCount: 1,
        unreadCount: 0,
      },
      {
        id: 'trash',
        userId: testUserId,
        name: '垃圾箱',
        type: 'trash',
        emailCount: 0,
        unreadCount: 0,
      },
    ];

    for (const folder of folders) {
      await pool.execute(`
        INSERT INTO folders (id, user_id, name, type, email_count, unread_count, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [folder.id, folder.userId, folder.name, folder.type, folder.emailCount, folder.unreadCount]);
    }

    // 创建示例邮件
    const emails = [
      {
        id: 'email-1',
        messageId: 'msg-1',
        userId: testUserId,
        folderId: 'inbox',
        subject: '欢迎使用邮箱系统',
        senderEmail: '<EMAIL>',
        senderName: '系统管理员',
        recipients: ['<EMAIL>'],
        ccRecipients: [],
        bccRecipients: [],
        contentText: '欢迎使用我们的邮箱系统！这是一个功能完整的邮件管理平台。',
        contentHtml: '<p>欢迎使用我们的邮箱系统！这是一个功能完整的邮件管理平台。</p>',
        isRead: false,
        isStarred: true,
        isDeleted: false,
        receivedAt: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
      },
      {
        id: 'email-2',
        messageId: 'msg-2',
        userId: testUserId,
        folderId: 'inbox',
        subject: '项目进度更新',
        senderEmail: '<EMAIL>',
        senderName: '项目经理',
        recipients: ['<EMAIL>'],
        ccRecipients: ['<EMAIL>'],
        bccRecipients: [],
        contentText: '本周项目进度顺利，已完成主要功能开发。',
        contentHtml: '<p>本周项目进度顺利，已完成主要功能开发。</p><ul><li>前端界面完成</li><li>后端API开发</li><li>数据库设计</li></ul>',
        isRead: true,
        isStarred: false,
        isDeleted: false,
        receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
      },
      {
        id: 'email-3',
        messageId: 'msg-3',
        userId: testUserId,
        folderId: 'inbox',
        subject: '会议邀请：周例会',
        senderEmail: '<EMAIL>',
        senderName: '会议助手',
        recipients: ['<EMAIL>'],
        ccRecipients: [],
        bccRecipients: [],
        contentText: '邀请您参加本周的例会，时间：周五下午2点。',
        contentHtml: '<p>邀请您参加本周的例会</p><p><strong>时间：</strong>周五下午2点</p><p><strong>地点：</strong>会议室A</p>',
        isRead: false,
        isStarred: false,
        isDeleted: false,
        receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6小时前
      },
      {
        id: 'email-4',
        messageId: 'msg-4',
        userId: testUserId,
        folderId: 'sent',
        subject: '回复：项目需求确认',
        senderEmail: '<EMAIL>',
        senderName: '测试用户',
        recipients: ['<EMAIL>'],
        ccRecipients: [],
        bccRecipients: [],
        contentText: '感谢您的需求确认，我们将按照讨论的方案进行开发。',
        contentHtml: '<p>感谢您的需求确认，我们将按照讨论的方案进行开发。</p>',
        isRead: true,
        isStarred: false,
        isDeleted: false,
        sentAt: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1天前
      },
      {
        id: 'email-5',
        messageId: 'msg-5',
        userId: testUserId,
        folderId: 'draft',
        subject: '待发送：月度总结报告',
        senderEmail: '<EMAIL>',
        senderName: '测试用户',
        recipients: ['<EMAIL>'],
        ccRecipients: [],
        bccRecipients: [],
        contentText: '本月工作总结...',
        contentHtml: '<p>本月工作总结...</p>',
        isRead: true,
        isStarred: false,
        isDeleted: false,
      },
    ];

    for (const email of emails) {
      await pool.execute(`
        INSERT INTO emails (
          id, message_id, user_id, folder_id, subject, sender_email, sender_name,
          recipients, cc_recipients, bcc_recipients, content_text, content_html,
          is_read, is_starred, is_deleted, received_at, sent_at, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [
        email.id,
        email.messageId,
        email.userId,
        email.folderId,
        email.subject,
        email.senderEmail,
        email.senderName,
        JSON.stringify(email.recipients),
        JSON.stringify(email.ccRecipients),
        JSON.stringify(email.bccRecipients),
        email.contentText,
        email.contentHtml,
        email.isRead,
        email.isStarred,
        email.isDeleted,
        email.receivedAt || null,
        email.sentAt || null,
      ]);
    }

    // 为第二封邮件添加附件
    await pool.execute(`
      INSERT INTO attachments (id, email_id, filename, content_type, size, url, created_at)
      VALUES (?, ?, ?, ?, ?, ?, NOW())
    `, ['att-1', 'email-2', '项目报告.pdf', 'application/pdf', 1024000, '/attachments/report.pdf']);

    // 创建示例联系人
    const contacts = [
      {
        id: 'contact-1',
        userId: testUserId,
        name: '张三',
        email: '<EMAIL>',
        phone: '13800138000',
        company: 'ABC公司',
        notes: '项目合作伙伴',
      },
      {
        id: 'contact-2',
        userId: testUserId,
        name: '李四',
        email: '<EMAIL>',
        phone: '13900139000',
        company: 'XYZ公司',
        notes: '技术顾问',
      },
    ];

    for (const contact of contacts) {
      await pool.execute(`
        INSERT INTO contacts (id, user_id, name, email, phone, company, notes, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `, [contact.id, contact.userId, contact.name, contact.email, contact.phone, contact.company, contact.notes]);
    }

    logger.info('Database seeding completed successfully');
  } catch (error) {
    logger.error('Failed to seed database:', error);
    throw error;
  }
};
